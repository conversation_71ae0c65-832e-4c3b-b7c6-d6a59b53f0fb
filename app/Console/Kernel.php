<?php
/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-01-11 10:11:22
 * @LastEditors: yanb
 * @LastEditTime: 2023-04-27 14:55:57
 */

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Carbon\Carbon;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();

        // 监控保司账号协议时间
        $schedule->command('account:expiring')->daily();

        // 没分钟同步一次保单数据.
        // $schedule->command('data:policy-aggregate')->everyFiveMinutes()->withoutOverlapping(5);

        // 每个月月初同步汇率.
        $schedule->command('data:sync-exchange-rate')->monthly();

        // 每天发送即将到期通知.
        $schedule->command('policy:send-will-expire-soon-notification')->dailyAt('09:00');

        // 每天同步排行榜数据
        $schedule->command('metric:ranking')->monthly();

        // 每天计算图表数据
        $schedule->command('metric:run')->dailyAt('00:00');

        // 发送东海保单生效邮件报备
        $schedule->command('policy:send-dic-mail')->dailyAt('23:59');

        // 检查用户余额
        $schedule->command('watchdog:check-balance')->everyFiveMinutes();

        // 每五分钟执行一次
        $schedule->command('finance:checking-policy-finance-data')->everyFiveMinutes();

        //每月月底也执行一次
        $schedule->command('finance:checking-policy-finance-data')->monthlyOn(Carbon::now()->daysInMonth, '23:59');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
