<?php

namespace App\Console\Commands\Metric\Metrics\Runners\User;

use App\Console\Commands\Metric\Metrics\Metric;

class ProductPremiumSummary extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'product_premium_summary';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        $dataset = $this->builder([], true)
            ->selectRaw("sum(policies.{$this->premiumColumn()}) as premium, fu.fake_id as user_id, fu.name as name, pt.name as product_type")
            ->groupBy(['fu.fake_id', 'pt.product_type'])
            ->get()
            ->groupBy('user_id');

        return $this->builder([], true)
            ->groupBy(['fu.fake_id'])
            ->selectRaw("fu.fake_id as user_id, fu.name as name, sum(policies.{$this->premiumColumn()}) as premium")
            ->get()
            ->map(fn($item) => [
                'user_id' => $item->user_id,
                'name' => $item->name,
                'premium' => $item->premium,
                'dataset' => $dataset->get($item->user_id, collect())->map(
                    fn($data) => [
                        'name' => $data->product_type,
                        'percent' => $item->premium > 0 ? round($data->premium / $item->premium * 100, 2) : 0,
                        'premium' => $data->premium,
                    ]
                )->toArray(),
            ])
            ->toArray();
    }
}
