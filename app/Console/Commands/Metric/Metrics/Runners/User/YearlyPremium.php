<?php

namespace App\Console\Commands\Metric\Metrics\Runners\User;

use App\Console\Commands\Metric\Metrics\Metric;

class YearlyPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'yearly_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder([], true)
            ->groupByRaw('fu.fake_id, year(policies.submitted_at)')
            ->selectRaw("
                fu.fake_id as user_id,
                fu.name as name,
                year(policies.submitted_at) as year,
                sum(policies.{$this->premiumColumn()}) as premium
            ")
            ->get()
            ->toArray();
    }
}
