<?php

namespace App\Console\Commands\Metric\Metrics\Runners\User;

use App\Console\Commands\Metric\Metrics\Metric;

class ProductPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'product_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->selectRaw("sum(policies.{$this->premiumColumn()}) as premium, fu.fake_id as user_id, fu.name as name, pt.name as product_type")
            ->groupBy(['fu.fake_id', 'pt.product_type',])
            ->get()
            ->toArray();
    }
}
