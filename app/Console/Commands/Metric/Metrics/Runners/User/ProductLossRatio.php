<?php

namespace App\Console\Commands\Metric\Metrics\Runners\User;

use App\Console\Commands\Metric\Metrics\Metric;

class ProductLossRatio extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'product_loss_ratio';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->leftJoin('claims as c', 'c.policy_no', '=', 'policies.policy_no')
            ->leftJoin('currencies as cs', 'cs.id', '=', 'c.claim_settlement_amount_currency_id')
            ->leftJoin('currencies as sc', 'sc.id', '=', 'c.settlement_costs_currency_id')
            ->leftJoin('currencies as cp', 'cp.id', '=', 'c.settlement_payment_amount_currency_id')
            ->leftJoin('currencies as clf', 'clf.id', '=', 'c.claim_lodging_fee_currency_id')
            ->selectRaw("
                fu.fake_id as user_id,
                fu.name as name,
                SUM(policies.{$this->premiumColumn()}) as premium,
                COALESCE(SUM(
                    CASE WHEN c.status IN (0,4,5,7)
                    THEN COALESCE(c.settlement_costs, 0) * COALESCE(sc.rate, 1)
                        + COALESCE(c.settlement_payment_amount, 0) * COALESCE(cp.rate, 1)
                    ELSE 0
                    END
                ), 0) AS settled_amount,
                COALESCE(SUM(
                    CASE WHEN c.status NOT IN (0,4,5,7)
                    THEN COALESCE(c.claim_settlement_amount, 0) * COALESCE(cs.rate, 1)
                        + COALESCE(c.claim_lodging_fee, 0) * COALESCE(clf.rate, 1)
                    ELSE 0
                    END
                ), 0) AS pending_amount,
                pt.name as product_type
            ")
            ->groupBy(['fu.fake_id', 'pt.product_type'])
            ->get()
            ->map(fn($item) => [
                'user_id' => $item->user_id,
                'name' => $item->name,
                'product_type' => $item->product_type,
                'settled_amount' => $item->settled_amount,
                'pending_amount' => $item->pending_amount,
                'premium' => $item->premium,
                'loss_ratio' => round(
                    $item->premium > 0 ? (($item->settled_amount + $item->pending_amount) / $item->premium * 100) : 0,
                    2
                )
            ])
            ->toArray();
    }
}
