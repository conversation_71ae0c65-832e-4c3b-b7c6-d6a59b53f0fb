<?php

namespace App\Console\Commands\Metric\Metrics\Runners\User;

use App\Console\Commands\Metric\Metrics\Runners\Runner as BaseRunner;

class Runner extends BaseRunner
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'user';
    }

    /**
     * 计算 runners.
     *
     * @return string[]
     */
    public function runners(): array
    {
        return [
            TotalPremium::class,
            ProductPremiumSummary::class,
            YearlyPremium::class,
            MonthlyPremium::class,
            ProductPremium::class,
            ProductCount::class,
            ProductLossRatio::class,
            ProductClaimRate::class
        ];
    }
}
