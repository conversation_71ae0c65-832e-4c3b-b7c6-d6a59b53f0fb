<?php

namespace App\Console\Commands\Metric\Metrics\Runners\User;

use App\Console\Commands\Metric\Metrics\Metric;

class MonthlyPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'monthly_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return [
            'current' => $this->data(),
            'last_year' => $this->data($this->lastYear()),
        ];
    }

    /**
     * 获取数据
     *
     * @param  array  $date
     *
     * @return \Illuminate\Support\Collection
     */
    protected function data(array $date = [])
    {
        return $this->builder($date)
            ->groupByRaw('fu.fake_id, month(policies.submitted_at)')
            ->selectRaw("
                fu.fake_id as user_id,
                fu.name as name,
                month(policies.submitted_at) as month,
                sum(policies.{$this->premiumColumn()}) as premium
            ")
            ->get();
    }

}
