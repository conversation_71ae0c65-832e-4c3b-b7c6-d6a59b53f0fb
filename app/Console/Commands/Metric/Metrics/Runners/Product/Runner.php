<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Product;

use App\Console\Commands\Metric\Metrics\Runners\Runner as BaseRunner;

class Runner extends BaseRunner
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'product';
    }

    /**
     * 计算 runners.
     *
     * @return string[]
     */
    public function runners(): array
    {
        return [
            TotalPremium::class,
            MonthlyPremium::class,
            CompanyPremium::class,
            CompanyCount::class,
            CompanyAvgRate::class,
            CompanyLossRatio::class,
        ];
    }
}
