<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Product;

use App\Console\Commands\Metric\Metrics\Metric;

class CompanyCount extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'company_count';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->left<PERSON>oin('fake_company_branches as cb', 'cb.id', '=', 'policies.company_branch_id')
            ->selectRaw("count(1) as count, cb.name as name, pt.product_type, pt.name as product_type_name")
            ->groupBy(['pt.product_type', 'cb.fake_id'])
            ->get()
            ->toArray();
    }
}
