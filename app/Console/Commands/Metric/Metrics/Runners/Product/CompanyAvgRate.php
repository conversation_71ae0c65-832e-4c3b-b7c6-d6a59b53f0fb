<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Product;

use App\Console\Commands\Metric\Metrics\Metric;

class CompanyAvgRate extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'company_avg_rate';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        $avgRate = $this->builder()
            ->leftJoin('fake_company_branches as cb', 'cb.id', '=', 'policies.company_branch_id')
            ->selectRaw("avg(policies.{$this->rateColumn()}) as avg_rate, cb.name, pt.product_type")
            ->groupBy(['pt.product_type', 'cb.fake_id'])
            ->get()
            ->groupBy('product_type');

        return $this->builder()
            ->selectRaw("avg(policies.{$this->rateColumn()}) as avg_rate, pt.product_type, pt.name as product_type_name")
            ->groupBy('pt.product_type')
            ->get()
            ->map(
                fn($item) => [
                    'avg_rate' => $item->avg_rate,
                    'product_type' => $item->product_type,
                    'product_type_name' => $item->product_type_name,
                    'company_avg_rate' => $avgRate->get($item->product_type, [])->toArray(),
                ]
            )->toArray();
    }
}
