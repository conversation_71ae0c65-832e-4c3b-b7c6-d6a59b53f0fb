<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Company;

use App\Console\Commands\Metric\Metrics\Metric;

class MonthlyPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'monthly_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return [
            'current' => $this->data(),
            'last_year' => $this->data($this->lastYear()),
        ];
    }

    /**
     * 获取数据
     *
     * @param  array  $date
     *
     * @return \Illuminate\Support\Collection
     */
    protected function data(array $date = [])
    {
        return $this->builder($date)
            ->leftJoin('companies as c', 'c.id', '=', 'policies.company_id')
            ->leftJoin('fake_company_branches as cb', 'cb.id', '=', 'policies.company_branch_id')
            ->groupByRaw('cb.fake_id, month(policies.submitted_at)')
            ->selectRaw("
                c.id as company_id,
                c.name as company_name,
                cb.fake_id as company_branch_id,
                cb.name as company_branch_name,
                month(policies.submitted_at) as month,
                sum(policies.{$this->premiumColumn()}) as premium
            ")
            ->get();
    }

}
