<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Company;

use App\Console\Commands\Metric\Metrics\Metric;

class ProductCount extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'product_count';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->left<PERSON>oin('fake_company_branches as cb', 'cb.id', '=', 'policies.company_branch_id')
            ->selectRaw("count(1) as count, policies.company_id, cb.fake_id as company_branch_id, cb.name as company_branch_name, pt.product_type, pt.name as name")
            ->groupBy(['cb.fake_id', 'pt.product_type'])
            ->get()
            ->toArray();
    }
}
