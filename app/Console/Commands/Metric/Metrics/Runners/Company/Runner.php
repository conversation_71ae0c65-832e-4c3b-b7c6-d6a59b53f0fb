<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Company;

use App\Console\Commands\Metric\Metrics\Runners\Runner as BaseRunner;

class Runner extends BaseRunner
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'product';
    }

    /**
     * 计算 runners.
     *
     * @return string[]
     */
    public function runners(): array
    {
        return [
            TotalPremium::class,
            MonthlyPremium::class,
            ProductPremium::class,
            ProductCount::class,
            ProductLossRatio::class,
            BranchPremium::class,
            BranchCount::class,
            BranchLossRatio::class,
        ];
    }
}
