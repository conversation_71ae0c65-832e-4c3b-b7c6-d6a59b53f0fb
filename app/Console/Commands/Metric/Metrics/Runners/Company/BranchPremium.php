<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Company;

use App\Console\Commands\Metric\Metrics\Metric;

class BranchPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'branch_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->leftJoin('fake_company_branches as cb', 'cb.id', '=', 'policies.company_branch_id')
            ->selectRaw("sum(policies.{$this->premiumColumn()}) as premium, policies.company_id as company_id, cb.fake_id as company_branch_id, cb.name as name")
            ->groupBy(['cb.fake_id'])
            ->get()
            ->toArray();
    }
}
