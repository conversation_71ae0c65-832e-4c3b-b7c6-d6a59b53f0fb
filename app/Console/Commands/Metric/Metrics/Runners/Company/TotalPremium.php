<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Company;

use App\Console\Commands\Metric\Metrics\Metric;

class TotalPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'total_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        $currentYear = $this->sumPremium();
        $lastYear = $this->sumPremium($this->lastYear())->keyBy('company_id');
        $receivablePremium = $this->sumReceivablePremium()->keyBy('company_id');

        return $currentYear->map(
            function ($item) use ($lastYear, $receivablePremium) {
                $item->last_year = $lastYear->get($item->company_id, (object) ['premium' => 0])->premium;

                $item->yoy = $item->premium > 0
                    ? round(($item->premium - $item->last_year) / $item->premium * 100, 2)
                    : 0;

                $receivablePremium = $receivablePremium->get(
                    $item->company_id,
                    (object) ['expected_premium' => 0, 'actual_premium' => 0]
                );

                $item->expected_premium = $receivablePremium['expected_premium'] ?? 0;
                $item->actual_premium = $receivablePremium['actual_premium'] ?? 0;

                return $item;
            }
        )->toArray();
    }

    /**
     * 计算应收&实收保费
     *
     * @return \Illuminate\Support\Collection
     */
    protected function sumReceivablePremium()
    {
        return $this->builder()
            ->leftJoin('premium_receivables as pr', 'pr.policy_id', '=', 'policies.id')
            ->leftJoin('premium_receivable_bills as prb', 'pr.bill_id', '=', 'prb.id')
            ->leftJoin('companies as c', 'c.id', '=', 'policies.company_id')
            ->whereNull('pr.deleted_at')
            ->groupBy(['policies.company_id'])
            ->selectRaw("
                policies.company_id,
                c.name,
                SUM(
                    CASE
                        WHEN pr.bill_id IS NULL
                        THEN pr.receivable
                        ELSE 0
                    END
                ) as expected_premium,
                SUM(
                    CASE
                        WHEN pr.bill_id IS NOT NULL AND prb.status = 2
                        THEN pr.receivable
                        ELSE 0
                    END
                ) as actual_premium
            ")
            ->get()
            ->map(fn($item) => [
                'company_id' => $item->company_id,
                'company_name' => $item->name,
                'expected_premium' => (int) $item->expected_premium,
                'actual_premium' => (int) $item->actual_premium,
            ]);
    }

    /**
     * 计算保费
     *
     * @param  mixed $date
     * @return \Illuminate\Support\Collection
     */
    protected function sumPremium(?array $date = null)
    {
        return $this->builder($date)
            ->leftJoin('companies as c', 'c.id', '=', 'policies.company_id')
            ->groupBy('policies.company_id')
            ->selectRaw("
                c.id as company_id,
                c.name as company_name,
                SUM(policies.{$this->premiumColumn()}) as premium
            ")
            ->get();
    }
}
