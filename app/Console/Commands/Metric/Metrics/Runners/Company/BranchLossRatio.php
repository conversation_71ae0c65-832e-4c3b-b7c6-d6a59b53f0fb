<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Company;

use App\Console\Commands\Metric\Metrics\Metric;

class BranchLossRatio extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'branch_loss_ratio';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->leftJoin('fake_company_branches as cb', 'cb.id', '=', 'policies.company_branch_id')
            ->leftJoin('claims as c', 'c.policy_no', '=', 'policies.policy_no')
            ->leftJoin('currencies as cs', 'cs.id', '=', 'c.claim_settlement_amount_currency_id')
            ->leftJoin('currencies as sc', 'sc.id', '=', 'c.settlement_costs_currency_id')
            ->leftJoin('currencies as cp', 'cp.id', '=', 'c.settlement_payment_amount_currency_id')
            ->leftJoin('currencies as clf', 'clf.id', '=', 'c.claim_lodging_fee_currency_id')
            ->groupBy(['cb.fake_id'])
            ->selectRaw("
                policies.company_id,
                cb.fake_id as company_branch_id,
                cb.name as name,
                SUM(policies.{$this->premiumColumn()}) as premium,
                COALESCE(SUM(
                    CASE WHEN c.status IN (0,4,5,7)
                    THEN COALESCE(c.settlement_costs, 0) * COALESCE(sc.rate, 1)
                        + COALESCE(c.settlement_payment_amount, 0) * COALESCE(cp.rate, 1)
                    ELSE 0
                    END
                ), 0) AS settled_amount,
                COALESCE(SUM(
                    CASE WHEN c.status NOT IN (0,4,5,7)
                    THEN COALESCE(c.claim_settlement_amount, 0) * COALESCE(cs.rate, 1)
                        + COALESCE(c.claim_lodging_fee, 0) * COALESCE(clf.rate, 1)
                    ELSE 0
                    END
                ), 0) AS pending_amount
            ")
            ->get()
            ->map(fn($item) => [
                'company_id' => $item->company_id,
                'company_branch_id' => $item->company_branch_id,
                'name' => $item->name,
                'premium' => $item->premium,
                'settled_amount' => $item->settled_amount,
                'pending_amount' => $item->pending_amount,
                'loss_ratio' => round(
                    $item->premium > 0 ? (($item->settled_amount + $item->pending_amount) / $item->premium * 100) : 0,
                    2
                )
            ])
            ->toArray();
    }
}
