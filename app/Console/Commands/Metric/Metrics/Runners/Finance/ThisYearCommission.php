<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Finance;

use App\Console\Commands\Metric\Metrics\Metric;

class ThisYearCommission extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'this_year_commission';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->leftJoin('poundage_payments as pp', 'pp.policy_id', '=', 'policies.id')
            ->leftJoin('poundage_payment_bills as ppb', 'pp.bill_id', '=', 'ppb.id')
            ->whereNull('pp.deleted_at')
            ->whereNull('ppb.deleted_at')
            ->selectRaw("
                SUM(pp.poundage) as total_commission,
                SUM(
                    CASE
                        WHEN pp.bill_id IS NULL
                        THEN pp.poundage
                        ELSE 0
                    END
                ) as expected_commission,
                SUM(
                    CASE
                        WHEN pp.bill_id IS NOT NULL AND ppb.status = 2
                        THEN pp.poundage
                        ELSE 0
                    END
                ) as actual_commission
            ")
            ->first()
            ->toArray();
    }
}
