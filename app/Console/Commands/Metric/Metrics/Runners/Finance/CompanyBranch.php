<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Finance;

use App\Console\Commands\Metric\Metrics\Metric;

class CompanyBranch extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'company_branch';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->leftJoin('fake_company_branches as cb', 'cb.id', '=', 'policies.company_branch_id')
            ->leftJoin('poundage_payments as pp', 'pp.policy_id', '=', 'policies.id')
            ->leftJoin('poundage_payment_bills as ppb', 'pp.bill_id', '=', 'ppb.id')
            ->leftJoin('premium_receivables as pr', 'pr.policy_id', '=', 'policies.id')
            ->leftJoin('premium_receivable_bills as prb', 'pr.bill_id', '=', 'prb.id')
            ->whereNull('pr.deleted_at')
            ->whereNull('pp.deleted_at')
            ->groupBy('cb.fake_id')
            ->selectRaw("
                cb.fake_id as com_id,
                cb.name as name,
                SUM(policies.{$this->premiumColumn()}) as total_premium,
                SUM(
                    CASE
                        WHEN pr.bill_id IS NULL
                        THEN pr.receivable
                        ELSE 0
                    END
                ) as expected_premium,
                SUM(
                    CASE
                        WHEN pr.bill_id IS NOT NULL AND prb.status = 2
                        THEN pr.receivable
                        ELSE 0
                    END
                ) as actual_premium,
                SUM(pp.poundage) as total_commission,
                SUM(
                    CASE
                        WHEN pp.bill_id IS NULL
                        THEN pp.poundage
                        ELSE 0
                    END
                ) as expected_commission,
                SUM(
                    CASE
                        WHEN pp.bill_id IS NOT NULL AND ppb.status = 2
                        THEN pp.poundage
                        ELSE 0
                    END
                ) as actual_commission
            ")
            ->get()
            ->toArray();
    }
}
