<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Finance;

use App\Console\Commands\Metric\Metrics\Metric;

class TotalPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'total_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        $premium = $this->sumPremium();
        $receivablePremium = $this->sumReceivablePremium();

        return [
            'premium' => $premium->premium ?? 0,
            'expected_premium' => $receivablePremium->expected_premium ?? 0,
            'actual_premium' => $receivablePremium->actual_premium ?? 0,
        ];
    }

    /**
     * 计算应收&实收保费
     *
     * @return \Illuminate\Support\Collection
     */
    protected function sumReceivablePremium()
    {
        return $this->builder([], true)
            ->leftJoin('premium_receivables as pr', 'pr.policy_id', '=', 'policies.id')
            ->leftJoin('premium_receivable_bills as prb', 'pr.bill_id', '=', 'prb.id')
            ->whereNull('pr.deleted_at')
            ->whereNull('prb.deleted_at')
            ->selectRaw("
                SUM(
                    CASE
                        WHEN pr.bill_id IS NULL OR prb.status != 2
                        THEN pr.receivable
                        ELSE 0
                    END
                ) as expected_premium,
                SUM(
                    CASE
                        WHEN pr.bill_id IS NOT NULL AND prb.status = 2
                        THEN pr.receivable
                        ELSE 0
                    END
                ) as actual_premium
            ")
            ->first();
    }

    /**
     * 计算保费
     *
     * @return \Illuminate\Support\Collection
     */
    protected function sumPremium()
    {
        return $this->builder([], true)
            ->selectRaw("SUM(policies.{$this->premiumColumn()}) as premium")
            ->first();
    }
}
