<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Finance;

use App\Console\Commands\Metric\Metrics\Runners\Runner as BaseRunner;

class Runner extends BaseRunner
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'finance';
    }

    /**
     * 计算 runners.
     *
     * @return string[]
     */
    public function runners(): array
    {
        return [
            TotalPremium::class,
            ThisYearPremium::class,
            TotalCommission::class,
            ThisYearCommission::class,
            CompanyBranch::class,
        ];
    }
}
