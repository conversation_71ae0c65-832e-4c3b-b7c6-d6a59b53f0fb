<?php

namespace App\Console\Commands\Metric\Metrics\Runners;

use App\Console\Commands\Metric\Metrics\Metric;

abstract class Runner extends Metric
{
    /**
     * 执行计算器
     *
     * @return array
     */
    public function run(): array
    {
        $data = [];
        foreach ($this->runners() as $runner) {
            $runner = new $runner($this->platformId, $this->dataSource, $this->year);
            $data[$runner->name()] = $runner->run();
        }

        return $data;
    }

    /**
     * 名称
     *
     * @return string
     */
    abstract public function name(): string;

    /**
     * 获取所有计算器
     *
     * @return array
     */
    abstract public function runners(): array;
}
