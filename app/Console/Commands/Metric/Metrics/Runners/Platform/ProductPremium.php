<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Platform;

use App\Console\Commands\Metric\Metrics\Metric;

class ProductPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'product_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->selectRaw("sum(policies.{$this->premiumColumn()}) as premium, pt.name as product_type")
            ->groupBy('pt.product_type')
            ->get()
            ->map(fn($item) => [
                'product_type' => $item->product_type,
                'premium' => $item->premium,
            ])
            ->toArray();
    }
}
