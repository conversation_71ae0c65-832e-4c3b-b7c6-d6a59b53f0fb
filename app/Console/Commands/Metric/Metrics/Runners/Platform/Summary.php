<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Platform;

use App\Console\Commands\Metric\Metrics\Metric;
use App\Models\User;

class Summary extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'summary';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return [
            'company_count' => $this->builder()->clone()->distinct('company_id')->count(),
            'company_branch_count' => $this->companyBranchCount(),
            'insured_user_count' => $this->builder()->clone()->distinct('user_id')->count(),
            'new_user_count' => $this->newUserCount(),
            'total_user_count' => $this->totalUserCount(),
        ];
    }

    /**
     * 公司分支机构数量
     *
     * @return int
     */
    protected function companyBranchCount(): int
    {
        return $this->builder()
            ->leftJoin('fake_company_branches as fb', 'fb.id', '=', 'policies.company_branch_id')
            ->distinct('fb.fake_id')
            ->count();
    }

    /**
     * 总用户数量
     *
     * @return int
     */
    protected function totalUserCount(): int
    {
        return User::where('platform_id', $this->platformId)->count();
    }

    /**
     * 新用户数量
     *
     * @return int
     */
    protected function newUserCount()
    {
        return User::where('platform_id', $this->platformId)
            ->whereBetween('created_at', $this->dateRange($this->year))
            ->count();
    }
}
