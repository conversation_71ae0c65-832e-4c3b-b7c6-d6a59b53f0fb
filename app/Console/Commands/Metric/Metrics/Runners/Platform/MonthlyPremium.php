<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Platform;

use App\Console\Commands\Metric\Metrics\Metric;

class MonthlyPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'monthly_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return [
            'current' => $this->data(),
            'last_year' => $this->data($this->lastYear()),
        ];
    }

    /**
     * 获取数据
     *
     * @param  array  $date
     *
     * @return array
     */
    protected function data(array $date = [])
    {
        return $this->builder($date)
            ->groupByRaw('month(policies.submitted_at)')
            ->selectRaw("month(policies.submitted_at) as month, sum(policies.{$this->premiumColumn()}) as premium")
            ->orderBy('month')
            ->get()
            ->map(fn($item) => [
                'month' => $item->month,
                'premium' => $item->premium,
            ])
            ->toArray();
    }

}
