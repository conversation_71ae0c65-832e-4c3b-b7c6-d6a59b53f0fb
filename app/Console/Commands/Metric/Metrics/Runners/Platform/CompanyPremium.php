<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Platform;

use App\Console\Commands\Metric\Metrics\Metric;

class CompanyPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'company_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->leftJoin('fake_company_branches as cb', 'cb.id', '=', 'policies.company_branch_id')
            ->selectRaw("sum(policies.{$this->premiumColumn()}) as premium, cb.name as name")
            ->groupBy('cb.fake_id')
            ->get()
            ->map(fn($item) => [
                'name' => $item->name,
                'premium' => $item->premium,
            ])
            ->toArray();
    }
}
