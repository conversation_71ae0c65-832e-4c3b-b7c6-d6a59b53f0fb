<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Platform;

use App\Console\Commands\Metric\Metrics\Runners\Runner as BaseRunner;

class Runner extends BaseRunner
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'platform';
    }

    /**
     * 计算 runners.
     *
     * @return string[]
     */
    public function runners(): array
    {
        return [
            Summary::class,
            TotalPremium::class,
            MonthlyPremium::class,
            ProductPremium::class,
            ProductCount::class,
            ProductLossRatio::class,
            CompanyPremium::class,
            CompanyLossRatio::class,
        ];
    }
}
