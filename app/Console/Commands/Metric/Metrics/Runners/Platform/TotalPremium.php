<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Platform;

use App\Console\Commands\Metric\Metrics\Metric;

class TotalPremium extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'total_premium';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        $data = [
            'premium' => [
                'current' => $this->sumPremium(),
                'last_year' => $this->sumPremium($this->lastYear()),
            ],
        ] + $this->sumReceivablePremium();


        $data['premium']['yoy'] = $data['premium']['current'] > 0
            ? round(($data['premium']['current'] - $data['premium']['last_year']) / $data['premium']['current'] * 100, 2)
            : 0;

        return $data;
    }

    /**
     * 计算应收&实收保费
     *
     * @return array
     */
    protected function sumReceivablePremium(): array
    {
        return $this->builder()
            ->leftJoin('premium_receivables as pr', 'pr.policy_id', '=', 'policies.id')
            ->leftJoin('premium_receivable_bills as prb', 'pr.bill_id', '=', 'prb.id')
            ->whereNull('pr.deleted_at')
            ->selectRaw("
                SUM(
                    CASE
                        WHEN pr.bill_id IS NULL
                        THEN pr.receivable
                        ELSE 0
                    END
                ) as expected_premium,
                SUM(
                    CASE
                        WHEN pr.bill_id IS NOT NULL AND prb.status = 2
                        THEN pr.receivable
                        ELSE 0
                    END
                ) as actual_premium
            ")
            ->first()
            ->toArray();
    }

    /**
     * 计算保费
     *
     * @param  mixed $date
     * @return int
     */
    protected function sumPremium(?array $date = null)
    {
        return $this->builder($date)->sum($this->premiumColumn());
    }
}
