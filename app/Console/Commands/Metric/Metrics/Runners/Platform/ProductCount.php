<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Platform;

use App\Console\Commands\Metric\Metrics\Metric;

class ProductCount extends Metric
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'product_count';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return $this->builder()
            ->groupBy('pt.product_type')
            ->selectRaw("count(1) as count, pt.name as product_type")
            ->get()
            ->map(fn($item) => [
                'product_type' => $item->product_type,
                'count' => $item->count,
            ])
            ->toArray();
    }
}
