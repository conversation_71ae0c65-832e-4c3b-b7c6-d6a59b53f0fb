<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim;

use App\Console\Commands\Metric\Metrics\Metric;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Analyzer;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\ProductType;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Concerns\HasQueryBuilder;
use DB;

class CompanyBranch extends Metric
{
    use HasQueryBuilder;

    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'company_branch';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return DB::table('fake_company_branches')
            ->get(['fake_id', 'id', 'name'])
            ->unique('name')
            ->map(function ($companyBranch) {
                $builder = $this->newBuilder()
                    ->clone()
                    ->whereIn('policies.company_branch_id', function ($q) use ($companyBranch) {
                        $q->from('fake_company_branches')
                            ->where('fake_id', $companyBranch->fake_id)
                            ->select('id');
                    });

                $data = (new Analyzer(
                    new ProductType($builder),
                    $this->premiumColumn()
                ))->analyze();

                return [
                    'id' => $companyBranch->fake_id,
                    'name' => $companyBranch->name,
                    'data' => $data
                ];
            })->toArray();
    }
}
