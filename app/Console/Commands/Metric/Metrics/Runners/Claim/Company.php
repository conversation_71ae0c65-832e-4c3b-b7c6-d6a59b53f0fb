<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim;

use App\Console\Commands\Metric\Metrics\Metric;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Analyzer;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\ProductType;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Concerns\HasQueryBuilder;
use App\Models\Company as ModelCompany;

class Company extends Metric
{
    use HasQueryBuilder;

    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'company';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return ModelCompany::all(['id', 'name'])->map(function ($company) {
            $builder = $this->newBuilder()->clone()->where('policies.company_id', $company->id);

            $data = (new Analyzer(
                new ProductType($builder),
                $this->premiumColumn()
            ))->analyze();

            return [
                'id' => $company->id,
                'name' => $company->name,
                'data' => $data
            ];
        })->toArray();
    }
}
