<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim;

use App\Console\Commands\Metric\Metrics\Metric;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Analyzer;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Destination;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Subject;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\TradeType;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\TransportMode;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Concerns\HasQueryBuilder;
use App\Models\Company;
use App\Models\CompanyBranch;
use DB;
use Illuminate\Database\Eloquent\Builder;

class ProductType extends Metric
{
    use HasQueryBuilder;

    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'product_type';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return [
            'default' => $this->analyze($this->newBuilder(true)->clone()),
            'company' => $this->withCompany($this->newBuilder(true)->clone()),
            'company_branch' => $this->withCompanyBranch($this->newBuilder(true)->clone()),
        ];
    }

    /**
     * 按照出单公司
     *
     * @param  Builder $builder
     * @return array
     */
    protected function withCompany(Builder $builder): array
    {
        return Company::all(['id', 'name'])->map(function ($company) use ($builder) {
            $builder = $builder->clone()->where('policies.company_id', $company->id);

            return [
                'id' => $company->id,
                'name' => $company->name,
                'data' => $this->analyze($builder, true, $company->id)
            ];
        })->toArray();
    }

    /**
     * 按照出单公司
     *
     * @param  Builder $builder
     * @return array
     */
    protected function withCompanyBranch(Builder $builder): array
    {
        return DB::table('fake_company_branches')
            ->get(['fake_id', 'id', 'company_id', 'name'])
            ->unique('name')
            ->map(function ($companyBranch) {
                $builder = $this->newBuilder()
                    ->clone()
                    ->whereIn('policies.company_branch_id', function ($q) use ($companyBranch) {
                        $q->from('fake_company_branches')
                            ->where('fake_id', $companyBranch->fake_id)
                            ->select('id');
                    });

                return [
                    'id' => $companyBranch->fake_id,
                    'name' => $companyBranch->name,
                    'data' => $this->analyze($builder, true, $companyBranch->company_id)
                ];
            })->toArray();
    }

    /**
     * 分析数据
     *
     * @param  Builder $builder
     * @param  bool $withTransportMode
     * @param  int|null $companyId
     * @return array
     */
    protected function analyze(Builder $builder, bool $withTransportMode = false, int|null $companyId = null): array
    {
        return collect([
            ['product_type' => '9001', 'name' => '国内货运险'],
            ['product_type' => '9002', 'name' => '国际货运险'],
            ['product_type' => '9007', 'name' => '跨境电商货运险'],
        ])->map(function ($item) use ($builder, $withTransportMode, $companyId) {
            $builder = $builder->clone()->where('pt.product_type', $item['product_type']);

            $data = [
                'destination' => (new Analyzer(
                    new Destination($builder->clone()),
                    $this->premiumColumn()
                ))->analyze(),
                'subject' => (new Analyzer(
                    new Subject($builder->clone()),
                    $this->premiumColumn()
                ))->analyze(),
                'trade_type' => (new Analyzer(
                    new TradeType($builder->clone()),
                    $this->premiumColumn()
                ))->analyze(),
            ];

            if ($withTransportMode) {
                $data['transport_mode'] = (new Analyzer(
                    new TransportMode($builder->clone(), $companyId),
                    $this->premiumColumn()
                ))->analyze();
            }

            return [
                'product_type' => $item['product_type'],
                'name' => $item['name'],
                'data' => $data
            ];
        })->toArray();
    }
}
