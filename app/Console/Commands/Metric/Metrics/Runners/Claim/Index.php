<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim;

use App\Console\Commands\Metric\Metrics\Metric;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Analyzer;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\ProductType;
use App\Console\Commands\Metric\Metrics\Runners\Claim\Concerns\HasQueryBuilder;

class Index extends Metric
{
    use HasQueryBuilder;

    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'index';
    }

    /**
     * 计算
     *
     * @return array
     */
    public function run(): array
    {
        return (new Analyzer(
            new ProductType($this->newBuilder()->clone()),
            $this->premiumColumn()
        ))->analyze();
    }
}
