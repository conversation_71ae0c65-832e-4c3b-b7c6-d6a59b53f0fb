<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer;

use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Contracts\DataProvider;
use App\Models\PolicyCargo;
use App\Models\TransportMethod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;


class TransportMode implements DataProvider
{
    /**
     * 名称
     *
     * @var array
     */
    protected array $names = [];

    public function __construct(
        protected Builder $builder,
        protected int $companyId,
        protected array $groupBy = [],
    ) {
        $this->loadNames();
    }

    /**
     * 加载名称
     *
     * @return void
     */
    protected function loadNames()
    {
        TransportMethod::where('company_id', $this->companyId)
            ->get()
            ->each(function ($item) {
                $this->names[$item->id] = $item->name;
            });
    }

    /**
     * 获取保单数
     *
     * @param  string  $value
     * @return int
     */
    public function policyCount(string $value): int
    {
        return $this->builder->clone()->where('pc.transport_method_id', $value)->count();
    }

    /**
     * 获取数据
     *
     * @return \Illuminate\Support\Collection
     */
    public function data(): Collection
    {
        return $this->builder->clone()
            ->whereNotNull('c.id')
            ->get()
            ->groupBy($this->groupBy + ['transport_method_id']);
    }

    /**
     * 获取名称
     *
     * @param  string  $value
     * @return string
     */
    public function name(string $value): string
    {
        return $this->names[$value] ?? $value;
    }
}
