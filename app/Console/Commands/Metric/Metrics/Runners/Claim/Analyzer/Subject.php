<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer;

use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Contracts\DataProvider;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use App\Models\Subject as ModelSubject;


class Subject implements DataProvider
{
    /**
     * 名称
     *
     * @var array
     */
    protected array $names = [];

    public function __construct(
        protected Builder $builder,
        protected array $groupBy = [],
    ) {
        $this->loadNames();
    }

    /**
     * 加载名称
     *
     * @return void
     */
    protected function loadNames()
    {
        $subjects = ModelSubject::all(['id', 'name']);

        $subjects->each(function ($subject) {
            $this->names[$subject->id] = $subject->name;
        });
    }

    /**
     * 获取保单数
     *
     * @param  string  $value
     * @return int
     */
    public function policyCount(string $value): int
    {
        return $this->builder->clone()->where('pc.subject_id', $value)->count();
    }

    /**
     * 获取数据
     *
     * @return \Illuminate\Support\Collection
     */
    public function data(): Collection
    {
        return $this->builder->clone()
            ->whereNotNull('c.id')
            ->get()
            ->groupBy($this->groupBy + ['subject_id']);
    }

    /**
     * 获取名称
     *
     * @param  string  $value
     * @return string
     */
    public function name(string $value): string
    {
        return $this->names[$value] ?? $value;
    }
}
