<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Contracts;

use Illuminate\Support\Collection;

interface DataProvider
{
    /**
     * 获取保单数
     *
     * @param  string  $value
     * @return int
     */
    public function policyCount(string $value): int;

    /**
     * 获取名称
     *
     * @param  string  $value
     * @return string
     */
    public function name(string $value): string;

    /**
     * 获取数据
     *
     * @return \Illuminate\Support\Collection
     */
    public function data(): Collection;
}
