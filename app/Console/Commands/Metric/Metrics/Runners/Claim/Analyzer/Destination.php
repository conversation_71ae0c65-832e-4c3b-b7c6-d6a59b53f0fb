<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer;

use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Contracts\DataProvider;
use App\Models\Policy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class Destination implements DataProvider
{
    public function __construct(
        protected Builder $builder,
        protected array $groupBy = [],
    ) {
    }

    /**
     * 获取保单数
     *
     * @param  string  $value
     * @return int
     */
    public function policyCount(string $value): int
    {
        return $this->builder
            ->clone()
            ->whereNotNull('c.id')
            ->get([
                'policies.type',
                'pc.destination'
            ])->filter(function ($item) use ($value) {
                $current = in_array($item->type, [Policy::TYPE_INTL, Policy::TYPE_CBEC])
                    ? explode('-', $item->destination)[1] ?? '未知'
                    : explode('-', $item->destination)[0] ?? '未知';

                return $current === $value;
            })->count();
    }

    /**
     * 获取数据
     *
     * @return \Illuminate\Support\Collection
     */
    public function data(): Collection
    {
        return $this->builder
            ->clone()
            ->whereNotNull('c.id')
            ->get()
            ->map(function ($item) {
                $item->destination = in_array($item->type, [Policy::TYPE_INTL, Policy::TYPE_CBEC])
                    ? explode('-', $item->destination)[1] ?? '未知'
                    : explode('-', $item->destination)[0] ?? '未知';

                return $item;
            })
            ->groupBy($this->groupBy + ['destination']);
    }

    /**
     * 获取名称
     *
     * @param  string  $value
     * @return string
     */
    public function name(string $value): string
    {
        return $value;
    }
}
