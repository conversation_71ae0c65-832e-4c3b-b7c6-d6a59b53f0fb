<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer;

use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Contracts\DataProvider;
use App\Models\Claim;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class Analyzer
{
    public function __construct(
        protected DataProvider $dataProvider,
        protected string $premiumColumn,
    ) {
    }

    /**
     * 分析数据
     *
     * @return array
     */
    public function analyze(): array
    {
        return $this->dataProvider->data()
            ->map(function ($items, $condition) {
                $premium = $items->sum($this->premiumColumn);

                $claims = $items->pluck('claims')->flatten();

                $settledAmount = 0;
                $settledCount = 0;
                $pendingAmount = 0;
                $pendingCount = 0;
                foreach ($claims as $claim) {
                    if (in_array($claim->status, Claim::$typeStatus[Claim::TYPE_SETTLEMENT])) {
                        $settledAmount += ($claim->settlement_costs ?? 0) * ($claim->sc_rate ?: 1)
                            + ($claim->settlement_payment_amount ?? 0) * ($claim->cp_rate ?: 1);

                        $settledCount++;
                    } else {
                        $pendingAmount += ($claim->claim_settlement_amount ?? 0) * ($claim->cs_rate ?: 1)
                            + ($claim->claim_lodging_fee ?? 0) * ($claim->clf_rate ?: 1);

                        $pendingCount++;
                    }
                }

                $claimCount = $claims->count();
                $policyCount = $this->dataProvider->policyCount($condition);

                return [
                    'key' => $condition,
                    'name' => $this->dataProvider->name($condition),
                    'loss_ratio' => round($premium > 0 ? ($settledAmount + $pendingAmount) / $premium : 0, 2),
                    'claim_rate' => round($policyCount > 0 ? $claimCount / $policyCount : 0, 2),
                    'premium' => round($premium / 100, 2),
                    'settled_amount' => round($settledAmount / 100, 2),
                    'avg_settled_amount' => round($settledAmount / $claimCount / 100, 2),
                    'pending_amount' => round($pendingAmount / 100, 2),
                    'avg_days' => $this->avgDays($claims),
                    'settled_count' => $settledCount,
                    'pending_count' => $pendingCount,
                ] + $this->amountCount($claims);
            })
            ->values()
            ->toArray();
    }

    /**
     * 金额统计
     *
     * @param  \Illuminate\Support\Collection  $claims
     * @return array
     */
    protected function amountCount(Collection $claims)
    {
        $items = $claims->map(function ($item) {
            $amount = 0;
            if (in_array($item->status, Claim::$typeStatus[Claim::TYPE_SETTLEMENT])) {
                if ($item->settlement_payment_amount > 0) {
                    $amount = ($item->settlement_payment_amount ?? 0) * ($item->cp_rate ?: 1);
                } elseif ($item->claim_settlement_amount > 0) {
                    $amount = ($item->claim_settlement_amount ?? 0) * ($item->cs_rate ?: 1)
                        + ($item->claim_lodging_fee ?? 0) * ($item->clf_rate ?: 1);
                } else {
                    $amount = ($item->loss_amount ?? 0) * ($item->la_rate ?: 1);
                }
            }
            return ['amount' => $amount / 100];
        });

        $underTenThousand = $items->filter(fn($item) => $item['amount'] < 10000)->count();
        $overThanFiftyThousand = $items->filter(fn($item) => $item['amount'] >= 50000)->count();
        $overThanOneHundredThousand = $items->filter(fn($item) => $item['amount'] >= 100000)->count();

        return [
            'under_ten_thousand' => $underTenThousand,
            'over_than_fifty_thousand' => $overThanFiftyThousand,
            'over_than_one_hundred_thousand' => $overThanOneHundredThousand
        ];
    }

    /**
     * 平均理赔时常
     *
     * @param  \Illuminate\Database\Eloquent\Collection $claims
     * @return float
     */
    protected function avgDays(Collection $claims)
    {
        $avgDays = $claims->map(function ($item) {
            $endDate = $item->logs->whereIn('status', [
                Claim::STATUS_CLAIM_PAYMENT,
                Claim::STATUS_CANCELLED,
                Claim::STATUS_ZERO_CLAIM,
                Claim::STATUS_REFUSED,
                Claim::STATUS_CLAIM_SETTLEMENT,
            ])->sortBy('id')->last();

            if (!$endDate) {
                return Carbon::parse($item['date_of_reporting'])->diffInSeconds(Carbon::now());
            }
            return Carbon::parse($item['date_of_reporting'])->diffInSeconds($endDate->started_at);
        })->avg();

        return round($avgDays ? $avgDays / 86400 : 0, 2);
    }
}
