<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer;

use App\Console\Commands\Metric\Metrics\Runners\Claim\Analyzer\Contracts\DataProvider;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;


class ProductType implements DataProvider
{
    /**
     * 名称
     *
     * @var array
     */
    protected array $names = [];

    public function __construct(
        protected Builder $builder,
        protected array $groupBy = [],
    ) {
        $this->loadNames();
    }

    /**
     * 加载名称
     *
     * @return void
     */
    protected function loadNames()
    {
        DB::table('policy_types')
            ->groupBy('product_type')
            ->pluck('name', 'product_type')
            ->each(function ($name, $productType) {
                $this->names[$productType] = $name;
            });
    }

    /**
     * 获取保单数
     *
     * @param  string  $value
     * @return int
     */
    public function policyCount(string $value): int
    {
        return $this->builder->clone()->where('pt.product_type', $value)->count();
    }

    /**
     * 获取数据
     *
     * @return \Illuminate\Support\Collection
     */
    public function data(): Collection
    {
        return $this->builder->clone()
            ->whereNotNull('c.id')
            ->get()
            ->groupBy($this->groupBy + ['product_type']);
    }

    /**
     * 获取名称
     *
     * @param  string  $value
     * @return string
     */
    public function name(string $value): string
    {
        return $this->names[$value] ?? $value;
    }
}
