<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim;

use App\Console\Commands\Metric\Metrics\Runners\Runner as BaseRunner;

class Runner extends BaseRunner
{
    /**
     * 名称
     *
     * @return string
     */
    public function name(): string
    {
        return 'claim';
    }

    /**
     * 计算 runners.
     *
     * @return string[]
     */
    public function runners(): array
    {
        return [
            Index::class,
            Company::class,
            CompanyBranch::class,
            ProductType::class,
        ];
    }
}
