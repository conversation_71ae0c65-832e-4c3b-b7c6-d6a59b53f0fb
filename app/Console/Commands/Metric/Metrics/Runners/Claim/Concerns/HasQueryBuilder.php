<?php

namespace App\Console\Commands\Metric\Metrics\Runners\Claim\Concerns;

use App\Models\Policy;

trait HasQueryBuilder
{
    /**
     * 查询构造器
     *
     * @param  bool  $onlyCargo
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function newBuilder(bool $onlyCargo = false)
    {
        return $this->builder()
            ->clone()
            ->leftJoin('policy_cargos as pc', 'policies.id', 'pc.policy_id')
            ->leftJoin('claims as c', 'c.policy_no', 'policies.policy_no')
            ->when(
                $onlyCargo,
                fn($q) => $q->whereIn('pt.product_type', [
                    9000 + Policy::TYPE_DOMESTIC,
                    9000 + Policy::TYPE_INTL,
                    9000 + Policy::TYPE_CBEC,
                ])
            )
            ->with([
                'claims' => function ($q) {
                    $q->leftJoin('currencies as la', 'la.id', '=', 'claims.loss_amount_currency_id')
                        ->leftJoin('currencies as cs', 'cs.id', '=', 'claims.claim_settlement_amount_currency_id')
                        ->leftJoin('currencies as sc', 'sc.id', '=', 'claims.settlement_costs_currency_id')
                        ->leftJoin('currencies as cp', 'cp.id', '=', 'claims.settlement_payment_amount_currency_id')
                        ->leftJoin('currencies as clf', 'clf.id', '=', 'claims.claim_lodging_fee_currency_id')
                        ->select([
                            'claims.id',
                            'claims.policy_no',
                            'claims.loss_amount',
                            'claims.loss_amount_currency_id',
                            'la.rate as la_rate',
                            'claims.claim_settlement_amount_currency_id',
                            'claims.claim_settlement_amount',
                            'cs.rate as cs_rate',
                            'claims.settlement_costs_currency_id',
                            'claims.settlement_costs',
                            'sc.rate as sc_rate',
                            'claims.settlement_payment_amount_currency_id',
                            'claims.settlement_payment_amount',
                            'cp.rate as cp_rate',
                            'claims.claim_lodging_fee_currency_id',
                            'claims.claim_lodging_fee',
                            'clf.rate as clf_rate',
                            'claims.date_of_reporting',
                            'claims.status',
                        ])
                        ->with(['logs']);
                }
            ])
            ->select([
                'policies.id',
                'policies.type',
                'policies.order_no',
                'policies.policy_no',
                'policies.premium',
                'policies.user_premium',
                'pc.destination',
                'pc.insure_type',
                'pc.subject_id',
                'pc.transport_method_id',
                'pt.product_type',
            ])
            ->clone();
    }
}
