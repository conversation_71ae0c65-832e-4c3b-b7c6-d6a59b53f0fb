<?php

namespace App\Console\Commands\Metric\Metrics;

use App\Console\Commands\Metric\Metrics\Contracts\Runnable;
use App\Enums\MetricDataSource;
use App\Enums\ProductFrom;
use App\Models\Policy;
use Carbon\Carbon;

abstract class Metric implements Runnable
{
    /**
     * 创建图表案例
     *
     * @param  int $platformId
     * @param  \App\Enums\MetricDataSource $dataSource
     * @param  int $year
     */
    public function __construct(
        public int $platformId,
        public MetricDataSource $dataSource,
        protected int $year
    ) {
    }

    /**
     * 保费字段
     *
     * @return string
     */
    protected function premiumColumn(): string
    {
        return $this->dataSource === MetricDataSource::OurUser
            ? 'user_premium'
            : 'premium';
    }

    /**
     * 费率字段
     *
     * @return string
     */
    protected function rateColumn(): string
    {
        return $this->dataSource === MetricDataSource::OurUser
            ? 'user_rate'
            : 'rate';
    }

    /**
     * 保单查询
     * @param  mixed $date
     * @param  bool  $withoutDate
     * @return Policy|\Illuminate\Database\Eloquent\Builder<Policy>
     */
    protected function builder(?array $date = null, $withoutDate = false)
    {
        return Policy::leftJoin('users as u', 'u.id', '=', 'policies.user_id')
            ->leftJoin('fake_users as fu', 'fu.id', '=', 'u.id')
            ->leftJoin('policy_types as pt', 'pt.policy_id', '=', 'policies.id')
            ->when(
                $this->dataSource === MetricDataSource::OurUser,
                fn($q) => $q->where('u.platform_id', $this->platformId),
                fn($q) => $q->where(
                    fn($q) =>
                    $q->where('policies.platform_id', $this->platformId)
                        ->orWhere(function ($q) {
                            $q->where('policies.platform_id', $this->platformId)
                                ->whereHas('policyOffline', fn($q) => $q->where('product_from', ProductFrom::Our->value));
                        })
                )
            )
            ->where('policies.status', Policy::STATUS_ISSUED)
            ->when(!$withoutDate, fn($q) => $q->whereBetween('policies.submitted_at', $date ?: $this->dateRange($this->year)));
    }

    /**
     * 去年同期
     *
     * @return array
     */
    protected function lastYear(): array
    {
        $now = Carbon::now();

        return [
            $now->clone()->subYear()->startOfYear(),
            $now->clone()->subYear(),
        ];
    }


    /**
     * 日期范围
     *
     * @param  int  $year
     *
     * @return array
     */
    protected function dateRange(int $year): array
    {
        $now = Carbon::now();
        if ($now->year === $year) {
            return [
                $now->clone()->startOfYear(),
                $now->clone()->startOfDay(),
            ];
        }

        return [
            Carbon::create($year, 1, 1)->startOfDay(),
            Carbon::create($year, 12, 31)->endOfDay(),
        ];
    }
}
