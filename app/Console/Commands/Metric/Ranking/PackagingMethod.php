<?php

namespace App\Console\Commands\Metric\Ranking;


use App\Console\Commands\Metric\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class PackagingMethod extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData([], ['policyCargo.packingMethod'], true)
            ->groupBy(fn($item, $key) => $item['policyCargo']['packingMethod']['name'] ?? '')
            ->map(function ($item, $id) {
                $packingMethodIds = $item->pluck('policyCargo.packing_method_id')
                    ->unique()
                    ->toArray();

                $claims = $this->loadClaims(
                    fn($query) =>
                    $query->whereHas('policyCargo', fn($q) => $q->whereIn('packing_method_id', $packingMethodIds))
                );

                return $this->marshal($id, $item, $claims);
            })
            ->values();
    }
}
