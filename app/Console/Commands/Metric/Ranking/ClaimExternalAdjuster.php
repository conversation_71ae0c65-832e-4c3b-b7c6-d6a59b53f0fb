<?php

namespace App\Console\Commands\Metric\Ranking;

use App\Console\Commands\Metric\Ranking\Concerns\InteractsWithClaim;
use Illuminate\Support\Collection;

class ClaimExternalAdjuster extends DataTable
{
    use InteractsWithClaim;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData()
            ->groupBy('external_adjuster')
            ->map(function ($item, $id) {
                return $this->marshal($id, $item);
            })
            ->values();
    }
}
