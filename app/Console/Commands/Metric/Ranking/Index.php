<?php

namespace App\Console\Commands\Metric\Ranking;

use App\Enums\MetricDataSource;
use App\Enums\ProductFrom;
use App\Models\Policy;
use App\Models\User;
use DB;
use Illuminate\Database\Query\Builder;
use \Illuminate\Support\Collection;
use App\Models\CompanyBranch;

class Index extends DataTable
{
    /**
     * 获取数据集
     *
     * @return  array
     */
    public function handle(): array
    {
        $dataset = [];
        $sourceData = $this->data();

        $dataset['by_user'] = $this->byUser();
        $dataset['by_policy_count'] = $sourceData->sortByDesc('policies_count')->take(100)->toArray();
        $dataset['by_premium'] = $sourceData->sortByDesc('premium')->take(100)->toArray();
        $dataset['by_rate_avg'] = $sourceData->sortByDesc('rate_avg')->take(100)->toArray();
        $dataset['by_claim_count'] = $sourceData->sortByDesc('claims_count')->take(100)->toArray();
        $dataset['by_claim_payment_rate'] = $sourceData->sortByDesc('claim_payment_rate')->take(100)->toArray();
        $dataset['by_claim_rate'] = $sourceData->sortByDesc('claim_rate')->take(100)->toArray();
        $dataset['by_claim_payment_amount'] = $sourceData->sortByDesc('claim_payment_amount')->take(100)->toArray();
        $dataset['by_five_ten_thousand_claim_amount_rate'] = $sourceData->sortByDesc('claim_five_ten_thousand_amount_rate')->take(100)->toArray();
        $dataset['by_claim_finished_rate'] = $sourceData->sortByDesc('claim_finished_rate')->take(100)->toArray();

        return $dataset;
    }

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData()
            ->map(function ($item) {
                $data = [
                    'name' => $item['name'],
                    'policies_count' => $item['policies_count'],
                    'claims_count' => $item['claims_count'],
                    // 保费（万）
                    'premium' => round($item['policies_sum_premium'] / 100, 2),
                    'rate_avg' => round($item['policies_avg_rate'], 2),
                    'rate_yearly_increase_rate' => $this->getRateYearlyIncreaseRate($item['policies']),
                    'claim_rate' => round(($item['policies_count'] > 0 ? $item['claims_count'] / $item['policies_count'] : 0) * 1000, 2),
                    // 赔款金额
                    'claim_payment_amount' => $this->claimPaymentAmount($item['claims']),
                    // 结案赔款
                    'claim_finished_settlement_payment_amount' => $this->claimFinishedSettementPaymentAmount($item['claims']),
                    // 立案赔款
                    'claim_pending_settlement_amount' => $this->claimPendingSettlementAmount($item['claims']),
                    'claim_five_ten_thousand_amount_count' => $this->claimFiveTenThousandAmountCount($item['claims']),
                    'claim_payment_rate' => $this->claimPaymentRate($item['claims'], $item['policies_sum_premium']),
                ];

                $data['claim_five_ten_thousand_amount_rate'] = $this->claimFiveTenThousandAmountRate($item['claims_count'], $data['claim_five_ten_thousand_amount_count']);
                $data['claim_finished_rate'] = $this->claimFinishedRate($item['claims_count'], $item['claims']);
                $data['claim_days'] = $this->avgClaimDays($item['claims']);

                return $data;
            });
    }

    /**
     * 准备用户相关数据.
     *
     * @return void
     */
    protected function prepare()
    {
        $whereCondition = $this->dataSource === MetricDataSource::OurProduct
            ? " AND policies.platform_id = $this->platformId "
            : '';

        if ($this->year) {
            $whereCondition .= " AND user_id in (SELECT id FROM users WHERE platform_id = $this->platformId AND YEAR(created_at) = $this->year) ";
        }

        $validUsersSql = <<<SQL
            CREATE TEMPORARY TABLE temp_valid_users AS
            SELECT
                salesman_id,
                COUNT(DISTINCT user_id) AS valid_users_count
            FROM
                policies
            WHERE
                status = 5 $whereCondition
            GROUP BY
                salesman_id;
        SQL;

        $premiumColumn = $this->premiumColumn();
        $premiumSql = <<<SQL
            CREATE TEMPORARY TABLE temp_premium_users AS
            SELECT
                salesman_id,
                COUNT(DISTINCT user_id) AS ten_thousand_premium_count
            FROM
            (
                SELECT
                    salesman_id,
                    user_id,
                    SUM($premiumColumn) AS sum_premium
                FROM
                    policies
                WHERE
                    status = 5 $whereCondition
                GROUP BY
                    salesman_id,
                    user_id,
                    YEAR(created_at)
                HAVING
                    sum_premium > 1000000
            ) AS t
            GROUP BY
            salesman_id;
        SQL;

        try {
            DB::unprepared('DROP TEMPORARY TABLE temp_valid_users');
        } catch (\Exception $e) {
            // ignore
        }
        DB::unprepared($validUsersSql);

        try {
            DB::unprepared('DROP TEMPORARY TABLE temp_premium_users');
        } catch (\Exception $e) {
            // ignore
        }
        DB::unprepared($premiumSql);
    }

    /**
     * 用户数据
     *
     * @return array
     */
    protected function byUser()
    {
        $this->prepare();

        return User::select([
            'users.salesman_id',
            DB::raw('count(*) as users_count'),
            'temp_valid_users.valid_users_count',
            'temp_premium_users.ten_thousand_premium_count',
        ])
            ->leftJoin('temp_valid_users', 'users.salesman_id', '=', 'temp_valid_users.salesman_id')
            ->leftJoin('temp_premium_users', 'users.salesman_id', '=', 'temp_premium_users.salesman_id')
            ->orderBy('users_count', 'desc')
            ->groupBy('salesman_id')
            ->when($this->year, fn($q) => $q->whereYear('created_at', $this->year))
            ->where('platform_id', $this->platformId)
            ->get()
            ->map(function ($data) {
                return [
                    'name' => $data['salesman']['name'],
                    'users_count' => $data['users_count'],
                    'valid_users_count' => $data['valid_users_count'] ?: 0,
                    'ten_thousand_premium_count' => $data['ten_thousand_premium_count'] ?: 0,
                ];
            })
            ->toArray();
    }

    /**
     * 获取数据集
     *
     * @return Collection
     */
    protected function sourceData(): Collection
    {
        return CompanyBranch::whereIn('id', fn($q) => $this->sourceDataExpression($q, 'company_branch_id'))
            ->with([
                'policies' => fn($q) => $q->whereIn('id', fn($q) => $this->sourceDataExpression($q, 'id')),
                'claims' => function ($q) {
                    $q->whereIn('policy_no', fn($q) => $this->sourceDataExpression($q, 'policy_no'))
                        ->with([
                            'policyCoverageCurrency',
                            'claimSettlementAmountCurrency',
                            'claimLodgingFeeCurrency',
                            'lossAmountCurrency',
                            'settlementPaymentAmountCurrency',
                            'settlementCostsCurrency',
                            'logs',
                            'company'
                        ]);
                }
            ])
            ->withCount([
                'policies' => fn($q) => $q->whereIn('id', fn($q) => $this->sourceDataExpression($q, 'id')),
                'claims' => fn($q) => $q->whereIn('policy_no', fn($q) => $this->sourceDataExpression($q, 'policy_no'))
            ])
            ->withSum([
                'policies' => fn($q) =>
                    $q->whereIn('id', fn($q) => $this->sourceDataExpression($q, 'id'))
            ], 'premium')
            ->withAvg([
                'policies' => fn($q) =>
                    $q->whereIn('id', fn($q) => $this->sourceDataExpression($q, 'id'))
            ], 'rate')
            ->get(['id', 'name'])
            ->toBase();
    }

    /**
     * 获取筛选条件
     *
     * @param  Builder  $query
     * @param  string  $column
     *
     * @return Builder
     */
    protected function sourceDataExpression(Builder $query, string $column): Builder
    {
        return $query->from('policies')
            ->leftJoin('policy_offline', 'policies.id', '=', 'policy_offline.policy_id')
            ->when($this->dataSource === MetricDataSource::OurProduct, function ($q) {
                $q->where(function ($q) {
                    $q->where('platform_id', $this->platformId)
                        ->orWhere(function ($q) {
                            $q->where('platform_id', $this->platformId)
                                ->where('policy_offline.product_from', ProductFrom::Our->value);
                        });
                });
            }, function ($q) {
                $q->where(function ($q) {
                    $q->whereIn(
                        'user_id',
                        fn($q) => $q->from('users')
                            ->select('id')
                            ->where('platform_id', $this->platformId)
                    )
                        ->orWhere(function ($q) {
                            $q->where('platform_id', $this->platformId)
                                ->where('policy_offline.product_from', ProductFrom::Thirdparty->value);
                        });
                });
            })
            ->where('policies.status', Policy::STATUS_ISSUED)
            ->when($this->year, fn($q) => $q->whereYear('policies.submitted_at', $this->year))
            ->select('policies.' . $column)
            ->distinct();
    }
}
