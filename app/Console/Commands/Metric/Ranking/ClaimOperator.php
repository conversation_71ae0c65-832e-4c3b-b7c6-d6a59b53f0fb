<?php

namespace App\Console\Commands\Metric\Ranking;

use App\Console\Commands\Metric\Ranking\Concerns\InteractsWithClaim;
use Illuminate\Support\Collection;

class ClaimOperator extends DataTable
{
    use InteractsWithClaim;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData(['operator'])
            ->groupBy('operator_id')
            ->map(function ($item, $id) {
                return $this->marshal($item->first()['operator']['name'] ?? '无', $item);
            })
            ->values();
    }
}
