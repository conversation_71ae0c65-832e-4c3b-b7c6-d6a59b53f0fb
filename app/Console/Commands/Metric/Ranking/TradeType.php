<?php

namespace App\Console\Commands\Metric\Ranking;

use App\Console\Commands\Metric\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;


class TradeType extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 贸易类型
     *
     * @var array
     */
    protected $tradeTypes = [
        -1 => '国内运输',
        1 => '出口运输',
        2 => '进口运输',
        3 => '境外运输'
    ];

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData([], ['policyCargo'], true)
            ->groupBy(fn($item, $key) => $this->tradeTypes[$item['policyCargo']['insure_type']])
            ->map(function ($item, $id) {
                $claims = $this->loadClaims(
                    fn($query) =>
                    $query->whereHas('policyCargo', fn($q) => $q->where('insure_type', $item->first()['policyCargo']['insure_type'] ?? '-1'))
                );

                return $this->marshal($id, $item, $claims);
            })
            ->values();
    }
}
