<?php

namespace App\Console\Commands\Metric\Ranking;

use App\Console\Commands\Metric\Ranking\Concerns\InteractsWithClaim;
use Illuminate\Support\Collection;

class ClaimPackagingMethod extends DataTable
{
    use InteractsWithClaim;

    /**
     * 包装方式
     *
     * @var array
     */
    protected array $packagingMethods = [
        -1 => '未知',
        0 => '未知',
        1 => '裸装（无运输包装）',
        2 => '简易包装（包装材料防护力弱，如塑料膜；或不能100%全部包裹住货物的包装）',
        3 => '运输包装完善',
    ];

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData()
            ->groupBy(fn($item) => $this->packagingMethods[$item['packaging_method']])
            ->map(function ($item, $id) {
                return $this->marshal($id, $item);
            })
            ->values();
    }
}
