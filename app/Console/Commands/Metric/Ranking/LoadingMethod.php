<?php

namespace App\Console\Commands\Metric\Ranking;


use App\Console\Commands\Metric\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class LoadingMethod extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData([], ['policyCargo.loadingMethod'], true)
            ->groupBy(fn($item, $key) => $item['policyCargo']['loadingMethod']['name'] ?? '其他')
            ->map(function ($item, $id) {
                $loadingMethodIds = $item->pluck('policyCargo.loading_method_id')
                    ->unique()
                    ->toArray();

                $claims = $this->loadClaims(
                    fn($query) =>
                    $query->whereHas('policyCargo', fn($q) => $q->whereIn('loading_method_id', $loadingMethodIds))
                );

                return $this->marshal($id, $item, $claims);
            })
            ->values();
    }
}
