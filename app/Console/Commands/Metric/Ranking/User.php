<?php

namespace App\Console\Commands\Metric\Ranking;

use App\Console\Commands\Metric\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class User extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData(['user_id'], ['user'])
            ->groupBy('user_id')
            ->map(function ($item, $id) {
                $claims = $this->loadClaims('user_id', $id);

                return $this->marshal($item->first()['user']['name'], $item, $claims);
            })
            ->values();
    }
}
