<?php

namespace App\Console\Commands\Metric;

use App\Console\Commands\Metric\Mapper\CompanyBranch;
use App\Console\Commands\Metric\Mapper\User;
use App\Console\Commands\Metric\Mapper\Mapper;
use App\Enums\MetricDataSource;
use App\Models\Metric;
use App\Models\Platform;
use App\Models\Policy;
use Arr;
use DB;
use Illuminate\Console\Command;

class MetricCommand extends Command
{
    /**
     * 缓存键名
     *
     * @var string
     */
    public const DATA_CACHE_KEY = 'metric:%d:%s:%s';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'metric:run
                            {--p|platform=* : 平台 ID}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '[数据] 统计图表';

    /**
     * 计算类
     *
     * @var array
     */
    protected $runners = [
        'platform' => Metrics\Runners\Platform\Runner::class,
        'product' => Metrics\Runners\Product\Runner::class,
        'company' => Metrics\Runners\Company\Runner::class,
        'user' => Metrics\Runners\User\Runner::class,
        'finance' => Metrics\Runners\Finance\Runner::class,
        'claim' => Metrics\Runners\Claim\Runner::class,
    ];

    /**
     * Handle the command.
     *
     *
     * @return void
     */
    public function handle()
    {
        ini_set('memory_limit', '4096M');

        $this->createView();
        $this->map();

        $platforms = Platform::when(
            $this->option('platform'),
            fn($q, $p) => $q->whereIn('id', Arr::wrap($p))
        )->get();

        $progressBar = $this->output->createProgressBar($platforms->count() * count($this->runners) * 2 * 2);

        foreach ($platforms as $platform) {
            foreach ($this->runners as $tab => $runner) {
                $progressBar->advance();
                foreach ([MetricDataSource::OurProduct, MetricDataSource::OurUser,] as $dataSource) {
                    $this->executeRunner($runner, $platform->id, $tab, $dataSource);

                    $progressBar->advance();
                }
            }
        }

        $progressBar->finish();
    }

    /**
     * 重新关联模型
     *
     * @return void
     */
    protected function map()
    {
        (new Mapper(new User()))->map();
        (new Mapper(new CompanyBranch()))->map();
    }

    /**
     * 创建视图
     *
     * @return void
     */
    protected function createView(): void
    {
        $viewExists = DB::table('information_schema.views')
            ->where('table_schema', DB::getDatabaseName())
            ->where('table_name', 'policy_types')
            ->exists();

        if (!$viewExists) {
            DB::unprepared($this->sql());
        }
    }

    /**
     * 临时表 SQL
     *
     * @return string
     */
    protected function sql(): string
    {
        return <<<SQL
            CREATE VIEW policy_types AS SELECT
                p.id AS policy_id,
                CASE
                    WHEN p.type <> 6 THEN 9000 + p.type
                    WHEN p.type = 6 THEN po.category_id
                    ELSE NULL
                END AS product_type,

                CASE
                    WHEN p.type = 1 THEN '国内货运险'
                    WHEN p.type = 2 THEN '国际货运险'
                    WHEN p.type = 3 THEN '单车责任险'
                    WHEN p.type = 4 THEN '其他'
                    WHEN p.type = 5 THEN '雇主责任险'
                    WHEN p.type = 6 THEN CONCAT('(线下)', poc.name)
                    WHEN p.type = 7 THEN '跨境电商货运险'
                    WHEN p.type = 8 THEN '邮包险(国内)'
                    WHEN p.type = 9 THEN '邮包险(国际)'
                    ELSE '未知'
                END AS `name`
            FROM
                policies p
                LEFT JOIN policy_offline po ON p.id = po.policy_id
                LEFT JOIN product_offline_categories poc ON po.category_id = poc.id;
        SQL;
    }

    /**
     * 执行计算
     *
     * @param  string  $runner
     * @param  int  $platformId
     * @param  string  $tab
     * @param  MetricDataSource  $dataSource
     * @return void
     */
    protected function executeRunner($runner, int $platformId, string $tab, MetricDataSource $dataSource): void
    {
        $years = $this->availableYears($platformId);

        foreach ($years as $year) {
            $metricKey = sprintf(self::DATA_CACHE_KEY, $platformId, $tab, "{$dataSource->value}:{$year}");
            // 非今年度的不重复计算
            if ($year !== (int) date('Y') && Metric::hasKey($metricKey)) {
                continue;
            }

            Metric::put(
                $metricKey,
                (new $runner($platformId, $dataSource, $year))->run()
            );
        }
    }

    /**
     * 可用年度
     *
     * @param  int  $platformId
     *
     * @return array
     */
    protected function availableYears(int $platformId): array
    {
        return Policy::whereHas('user', fn($q) => $q->where('platform_id', $platformId))
            ->where('status', Policy::STATUS_ISSUED)
            ->groupByRaw('year(submitted_at)')
            ->selectRaw('year(submitted_at) as year')
            ->orderByDesc('year')
            ->get('year')
            ->pluck('year')
            ->filter()
            ->toArray();
    }
}
