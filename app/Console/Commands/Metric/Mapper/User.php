<?php

namespace App\Console\Commands\Metric\Mapper;

use App\Models\User as ModelUser;
use App\Console\Commands\Metric\Mapper\Contracts\Mappable;
use Illuminate\Database\Eloquent\Model;

class User implements Mappable
{
    /**
     * 关联模型
     *
     * @return \App\Models\User
     */
    public function model(): Model
    {
        return app(ModelUser::class);
    }

     /**
     * 需要额外查询的字段.
     *
     * @return array<string, \Closure(\Illuminate\Database\Schema\Blueprint)>
     */
    public function withColumns(): array
    {
        return [];
    }

    /**
     * Group by
     *
     * @param  mixed $value
     * @return string
     */
    public function groupBy(mixed $value): string
    {
        return $value->name;
    }
}
