<?php

namespace App\Console\Commands\Metric\Mapper;

use App\Console\Commands\Metric\Mapper\Contracts\Mappable;
use DB;
use Illuminate\Database\Schema\Blueprint;
use Schema;
use Symfony\Component\Uid\Uuid;

class Mapper
{
    public function __construct(protected Mappable $mapper)
    {
    }

    /**
     * 重新映射数据.
     *
     * @return void
     */
    public function map()
    {
        $columns = array_keys($this->mapper->withColumns());

        $values = $this->mapper->model()
            ->get(['id', 'name', ...$columns])
            ->groupBy(fn($value) => $this->mapper->groupBy($value));

        $mapping = [];

        foreach ($values as $name => $children) {
            $fakeId = md5($name);
            foreach ($children as $child) {
                $mapping[] = ['id' => $child->id, 'fake_id' => $fakeId, 'name' => $name, ...$child->only($columns)];
            }
        }

        $table = 'fake_' . $this->mapper->model()->getTable();

        $this->createTable($table);

        DB::table($table)->insert($mapping);
    }

    /**
     * 创建映射表
     *
     * @param  string $table
     * @return void
     */
    protected function createTable(string $table)
    {
        Schema::dropIfExists($table);

        Schema::create($table, function (Blueprint $table) {
            $table->bigInteger('id');
            $table->string('fake_id');
            $table->string('name');

            foreach ($this->mapper->withColumns() as $callback) {
                $callback($table);
            }
        });
    }
}
