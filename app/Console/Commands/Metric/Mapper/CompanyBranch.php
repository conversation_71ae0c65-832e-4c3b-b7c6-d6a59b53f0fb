<?php

namespace App\Console\Commands\Metric\Mapper;

use App\Models\CompanyBranch as ModelCompanyBranch;
use App\Console\Commands\Metric\Mapper\Contracts\Mappable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Schema\Blueprint;

class CompanyBranch implements Mappable
{
    /**
     * 关联模型
     *
     * @return \App\Models\CompanyBranch
     */
    public function model(): Model
    {
        return app(ModelCompanyBranch::class);
    }

    /**
     * 需要额外查询的字段.
     *
     * @return array<string, \Closure(\Illuminate\Database\Schema\Blueprint)>
     */
    public function withColumns(): array
    {
        return [
            'company_id' => fn (Blueprint $table) => $table->integer('company_id'),
        ];
    }

    /**
     * Group by
     *
     * @param  mixed $value
     * @return string
     */
    public function groupBy(mixed $value): string
    {
        preg_match('/^([^\d（-]+)/u', $value->name, $matches);

        return $matches[1];
    }
}
