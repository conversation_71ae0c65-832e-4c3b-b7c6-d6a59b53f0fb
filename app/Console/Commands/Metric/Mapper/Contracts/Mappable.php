<?php

namespace App\Console\Commands\Metric\Mapper\Contracts;

use Illuminate\Database\Eloquent\Model;

interface Mappable
{
    /**
     * 关联模型
     *
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function model(): Model;

    /**
     * 需要额外查询的字段.
     *
     * @return array<string, \Closure(\Illuminate\Database\Schema\Blueprint)>
     */
    public function withColumns(): array;

    /**
     * Group by
     *
     * @param  mixed $value
     * @return string
     */
    public function groupBy(mixed $value): string;
}
