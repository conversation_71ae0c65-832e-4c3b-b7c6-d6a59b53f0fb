<?php

namespace App\Enums;

enum InquiryLoadingPosition: int
{
    case Unknown = -1; // 无

    case OnDeck = 1; // 甲板

    case UnderDeck = 2; // 舱内

    /**
     * Get the text representation of the enum value.
     *
     * @return string
     */
    public function text(): string
    {
        return match ($this) {
            self::Unknown => '无',
            self::OnDeck => '甲板',
            self::UnderDeck => '舱内',
        };
    }
}
