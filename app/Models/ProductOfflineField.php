<?php

namespace App\Models;

use App\Enums\ProductOfflineFieldType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\QueryBuilder\QueryBuilder;

class ProductOfflineField extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'category_id',
        'type',
        'title',
        'name',
        'order',
        'options',
        'file',
    ];

    protected $casts = [
        'type' => ProductOfflineFieldType::class,
    ];

    /**
     * 获取字段列表
     *
     * @param  int $categoryId
     * @param  bool $pageable
     * @return mixed
     */
    public function getFields($categoryId, $pageable = true)
    {
        $builder = QueryBuilder::for($this)
            ->allowedFilters([
                'title',
                'name',
                'type',
            ])
            ->where('category_id', $categoryId)
            ->orderBy('order', 'desc');

        return $pageable ? $builder->paginate() : $builder->get();
    }
}
