<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Metric extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'key',
        'data',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'data' => 'array',
    ];

    /**
     * 储存数据
     *
     * @param  string  $key
     * @param  mixed  $data
     *
     * @return \App\Models\Metric
     */
    public static function put(string $key, mixed $data)
    {
        return static::updateOrCreate(['key' => $key], ['data' => $data]);
    }

    /**
     * 检查是否存在
     *
     * @param  string  $key
     *
     * @return bool
     */
    public static function hasKey(string $key): bool
    {
        return static::where('key', $key)->exists();
    }

    /**
     * 获取数据
     *
     * @param  string  $key
     *
     * @return array|null
     */
    public static function retrieve(string $key): ?array
    {
        return static::where('key', $key)->latest('id')->value('data');
    }
}

