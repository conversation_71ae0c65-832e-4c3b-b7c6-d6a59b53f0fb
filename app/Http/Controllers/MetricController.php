<?php

namespace App\Http\Controllers;

use App\Enums\MetricDataSource;
use App\Enums\MetricPanel;
use App\Exports\DataRankingExport;
use App\Models\Metric;
use App\Services\Metric\Claim\Metric as ClaimMetric;
use App\Services\Metric\FilterOption;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\Rule;

class MetricController extends Controller
{
    /**
     * 获取统计数据
     *
     * @param  Request  $request
     *
     * @return JsonResource
     */
    public function index(Request $request)
    {
        $attributes = $request->validate([
            'datasource' => ['required', Rule::enum(MetricDataSource::class)],
            'panel' => ['required', Rule::enum(MetricPanel::class)],
            'year' => 'nullable|integer',
            'user_id' => [Rule::requiredIf($request->input('panel') === MetricPanel::User->value)],
        ], [
            'user_id.required' => '请选择用户',
        ]);

        $attributes['year'] = ($attributes['year'] ?? null) ?: date('Y');

        $metricKey = sprintf(
            'metric:%d:%s:%s:%s',
            Auth::user()['platform_id'],
            $attributes['panel'],
            $attributes['datasource'],
            $attributes['year']
        );

        $data = Metric::retrieve($metricKey);

        $data = match ($attributes['panel']) {
            MetricPanel::User->value => $this->filterUser($data, $attributes['user_id']),
            default => $data,
        };

        return JsonResource::make($data);
    }

    /**
     * 过滤用户数据
     *
     * @param  array  $data
     * @param  string  $uid
     *
     * @return array
     */
    protected function filterUser(array $data, string $uid): array
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $isTwoDimensional = !empty($value) && array_reduce(
                    $value,
                    fn($carry, $item) => $carry && is_array($item) && array_key_exists('user_id', $item),
                    true
                );

                $data[$key] = $isTwoDimensional
                    ? array_values(array_filter($value, fn($item) => $item['user_id'] === $uid))
                    : $this->filterUser($value, $uid);
            }
        }

        return $data;
    }

    /**
     * 获取排行榜数据
     *
     * @param  Request  $request
     *
     * @return array
     */
    protected function getRankingData(Request $request)
    {
        $attributes = $request->validate([
            'by' => 'required|string',
            'datasource' => ['required', Rule::enum(MetricDataSource::class)],
            'year' => 'nullable'
        ]);

        $metricKey = sprintf(
            'metric:ranking:%d:%s:%s',
            Auth::user()['platform_id'],
            $attributes['by'],
            $attributes['datasource']
        );

        if ((int) ($attributes['year'] ?? -1) !== -1) {
            $metricKey .= ':' . $attributes['year'];
        }

        return Metric::retrieve($metricKey);
    }

    /**
     * 排行榜数据统计
     *
     * @param  Request  $request
     *
     * @return JsonResource
     */
    public function ranking(Request $request)
    {
        return new JsonResource($this->getRankingData($request));
    }

    /**
     * 排行榜数据下载
     *
     * @param  Request  $request
     *
     * @return \Illuminate\Http\Response|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    protected function rankingDownload(Request $request)
    {
        $attributes = $request->validate([
            'user_label' => 'required|string',
            'label' => 'required|string',
            'sheets' => 'required|array',
        ]);

        $data = $this->getRankingData($request);

        return (
            new DataRankingExport(
                $attributes['sheets'],
                $attributes['label'],
                $attributes['user_label'],
                $data
            )
        )->download('数据看版排行榜.xlsx');
    }

    /**
     * 获取筛选项.
     *
     * @param  Request  $request
     * @param  \App\Services\Metric\FilterOption  $filterOption
     *
     * @return JsonResource
     */
    public function filterOptions(Request $request, FilterOption $filterOption)
    {
        $data = $request->input('module') === 'user'
            ? $filterOption->searchUsers()
            : $filterOption->options();

        return new JsonResource($data);
    }
}
