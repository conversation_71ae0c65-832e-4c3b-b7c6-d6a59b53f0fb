<?php

namespace App\Services\Policy;

use App\Enums\InquiryLoadingMethod;
use App\Enums\InquiryStatus;
use App\Enums\InquiryTransportMode;
use App\Models\Currency;
use App\Models\Inquiry;
use App\Models\Policy;
use App\Models\PolicyCargo;
use Auth;
use Illuminate\Support\Arr;

class CreateInquiry
{
    /**
     * 询价单主表暂存字段.
     *
     * @var array
     */
    public array $inquiryFields = [
        'admin_id' => null,
        'platform_id' => -1,
        'user_id' => -1,
        'policy_id' => null,
        'inquiry_number' => '',
        'policyholder_name' => '',
        'insured_name' => '',
        'goods_name' => '',
        'goods_type' => '',
        'packaging_and_quantity' => '',
        'goods_value' => 0,
        'goods_value_currency' => 'CNY',
        'departure' => '',
        'departure_address' => '',
        'transit' => '',
        'transit_address' => '',
        'destination' => '',
        'destination_address' => '',
        'departure_date' => null,
        'insurance_type' => '',
        'transport_mode' => -1,
        'loading_method' => -1,
        'vessel_info' => '',
        'loading_position' => '',
        'stowage_plan' => '',
        'land_transport_plan' => '',
        'include_warehouse_handling' => -1,
        'has_special_transport_reqs' => -1,
        'is_new_goods' => 1,
        'dimensions' => '',
        'photos' => null,
        'remarks' => '',
        'alert' => '',
        'status' => InquiryStatus::Draft,
    ];

    /**
     * 从保单创建询价单暂存单
     *
     * @param  int $policyId
     * @return \App\Models\Inquiry
     */
    public function handle(int $policyId)
    {
        $policy = Policy::ofPlatform()->with(['user', 'policyCargo', 'inquiry'])->findOrFail($policyId);

        abort_if($policy->inquiry, 409, '询价单已存在');

        abort_if($policy->status !== Policy::STATUS_SUBMITTED, 409, '当前保单状态不支持创建询价单');

        $data = $this->data($policy);

        $inquiry = $this->create($data);

        return $inquiry;
    }

    /**
     * 创建询价单暂存单
     *
     * @param  array $data
     * @return \App\Models\Inquiry
     */
    protected function create(array $data)
    {
        return Inquiry::create(Arr::only($data, array_keys($this->inquiryFields)));
    }

    /**
     * 询价单数据
     *
     * @param  \App\Models\Policy $policy
     * @return array
     */
    protected function data(Policy $policy): array
    {
        $policyCargo = $policy->policyCargo;

        $data = [
            'platform_id' => $policy->platform_id,
            'admin_id' => Auth::id(),
            'user_id' => $policy->user_id,
            'policy_id' => $policy->id,
            'inquiry_number' => order_no(''),
            'policyholder_name' => $policy->policyholder ?: '',
            'insured_name' => $policy->insured ?: '',
            'goods_name' => $policyCargo->goods_name ?: '',
            'packaging_and_quantity' => $policyCargo->goods_amount ?: '',
            'goods_value' => $policy->coverage / 100,
            'goods_value_currency' => $this->getGoodsValueCurrency($policyCargo),
            'departure' => $this->addAddressPrefixes($policyCargo->departure ?: '', $policyCargo->insure_type),
            'departure_address' => $this->extractAddress($policyCargo->departure, $policyCargo->departure_port),
            'transit' => $this->addAddressPrefixes($policyCargo->transmit ?: '', $policyCargo->insure_type),
            'transit_address' => $this->extractAddress($policyCargo->transmit, $policyCargo->transmit_port),
            'destination' => $this->addAddressPrefixes($policyCargo->destination ?: '', $policyCargo->insure_type),
            'destination_address' => $this->extractAddress($policyCargo->destination, $policyCargo->destination_port),
            'departure_date' => $policyCargo->shipping_date,
            'transport_mode' => $this->guessTransportMode($policyCargo),
            'loading_method' => $this->guessLoadingMethod($policyCargo),
            'is_new_goods' => $policyCargo->is_new ?? 1,
            'remarks' => $policy->remark ?: '',
            'status' => InquiryStatus::Draft,
        ];

        return $this->fillValues($this->inquiryFields, $data);
    }

    /**
     * 提取详细地址
     *
     * @param  string|null $address
     * @param  string|null $port
     * @return string|null
     */
    protected function extractAddress(?string $address, ?string $port): string|null
    {
        if (str_contains($address, ":")) {
            return last(explode(":", $address));
        }

        return $port;
    }

    /**
     * 推测装载方式.
     *
     * @param \App\Models\PolicyCargo $policyCargo
     * @return InquiryLoadingMethod
     */
    protected function guessLoadingMethod(PolicyCargo $policyCargo): InquiryLoadingMethod
    {
        $name = $policyCargo->loadingMethod->name;

        return match (true) {
            str_starts_with($name, '厢式货车') => InquiryLoadingMethod::BoxTruck,
            str_starts_with($name, '非厢式货车') => InquiryLoadingMethod::NonBoxTruck,
            str_starts_with($name, '集装箱') => InquiryLoadingMethod::ContainerTransport,
            str_starts_with($name, '非集装箱') => InquiryLoadingMethod::NonContainerTransport,
            default => InquiryLoadingMethod::Unknown,
        };
    }

    /**
     * 推测运输方式.
     *
     * @param \App\Models\PolicyCargo $policyCargo
     * @return InquiryTransportMode
     */
    protected function guessTransportMode(PolicyCargo $policyCargo): InquiryTransportMode
    {
        $name = $policyCargo->transportMethod->name;

        return match (true) {
            str_contains($name, '水'), str_contains($name, '海') => InquiryTransportMode::Sea,
            str_contains($name, '空') => InquiryTransportMode::Air,
            str_contains($name, '陆'), str_contains($name, '公路') => InquiryTransportMode::Land,
            default => InquiryTransportMode::Sea,
        };
    }

    /**
     * 获取货物价值币种
     *
     * @param   \App\Models\PolicyCargo  $policyCargo
     * @return  string
     */
    protected function getGoodsValueCurrency(PolicyCargo $policyCargo): string
    {
        if ($policyCargo->invoice_currency_id > 0) {
            $currency = Currency::find($policyCargo->invoice_currency_id);
            return $currency ? $currency->code : 'CNY';
        }

        return 'CNY';
    }

    /**
     * 添加地址前缀
     *
     * @param   string  $address
     * @param   int     $insureType
     * @return  string
     */
    protected function addAddressPrefixes(string $address, int $insureType): string
    {
        if (!$address) {
            return '';
        }

        if (str_starts_with($address, 'ASCN')) {
            return '';
        }

        // 根据保障类型添加相应前缀
        switch ($insureType) {
            case PolicyCargo::INSURE_TYPE_DOMESTIC:
                return "中国大陆-$address";
            case PolicyCargo::INSURE_TYPE_EXPORT:
            case PolicyCargo::INSURE_TYPE_IMPORT:
            case PolicyCargo::INSURE_TYPE_OVERSEAS:
                // 对于国际运输，需要根据具体情况判断是否添加前缀
                if (!str_starts_with($address, '中国大陆-') && !str_starts_with($address, '境外地区-')) {
                    return "境外地区-$address";
                }
                break;
        }

        return $address;
    }

    /**
     * Fill values.
     *
     * @param   array  $fields
     * @param   array  $attributes
     * @return  array
     */
    protected function fillValues(array $fields, array $attributes)
    {
        $values = [];
        foreach ($fields as $field => $defaultValue) {
            $values[$field] = $attributes[$field] ?? $defaultValue;
        }

        return $values;
    }
}
