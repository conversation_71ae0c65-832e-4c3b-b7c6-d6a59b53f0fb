<?php

namespace App\Services\Policy\Offline\ImportTempateGenerator;

use App\Enums\ProductOfflineFieldType;
use App\Models\Admin;
use App\Models\Channel;
use App\Models\Company;
use App\Models\CompanyBranch;
use App\Models\Currency;
use App\Models\ProductOfflineCategory;
use App\Models\ProductOfflineField;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\IOFactory as XlsxIOFactory;
use PhpOffice\PhpSpreadsheet\NamedRange;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

abstract class CommonGenerator
{

    /**
     * 类别ID
     *
     * @var
     */
    protected $categoryId;

    /**
     * 生成模板
     *
     * @param   CompanyBranch  $company
     * @param   Collection     $products
     *
     * @return  string
     */
    public function handle(): string
    {
        $companies = $this->getCompanies();
        $companyBranches = $this->getCompanyBranches();
        $channels = $this->getChannels();
        $sales = $this->getSales();
        $agnets = $this->getagents();
        $users = $this->getUsers();
        $currencies = $this->getCurrencies();
        $xlsx = XlsxIOFactory::load($this->templatePath());
        $xlsx = $xlsx->copy();


        $this->setSheetContent($xlsx, '保险公司', $companies);
        $this->setCompanyBranchContent($xlsx, $companyBranches);
        $this->setSheetContent($xlsx, '投保渠道', $channels);
        $this->setSheetContent($xlsx, '业务员', $sales);
        $this->setCurrencyContent($xlsx, '保额币种', $currencies);
        $this->setUserOrAgentContent($xlsx, '代理商', $agnets);
        $this->setUserOrAgentContent($xlsx, '投保用户', $users);
        $this->setOtherColumns($xlsx);

        $xlsx->setActiveSheetIndex(0);

        $tempPath = sys_get_temp_dir() . '/' . uniqid() . '.xlsx';
        $writer = XlsxIOFactory::createWriter($xlsx, 'Xlsx');
        $writer->save($tempPath);

        return $tempPath;
    }

    /**
     * 获取保险公司
     *
     * @return mixed
     */
    protected function getCompanies()
    {
        return Company::enabled()->get();
    }

    /**
     * 获取出单公司
     *
     * @return mixed
     */
    protected function getCompanyBranches()
    {
        return CompanyBranch::where('is_enabled', 1)->with('company')->get();
    }

    /**
     * 获取渠道
     *
     * @return mixed
     */
    protected function getChannels()
    {
        return Channel::where('is_enabled', 1)->get();
    }

    /**
     * 获取业务员
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getSales()
    {
        return (new Admin())->getSales();
    }

    /**
     * 获取投保用户
     *
     * @return mixed
     */
    protected function getUsers()
    {
        return User::ofPlatform()
            ->where('agent_id', -1)
            ->where('is_agent', 0)
            ->with('salesman')
            ->get(['id', 'salesman_id', 'name']);
    }

    /**
     * 获取代理商
     *
     * @return mixed
     */
    protected function getAgents()
    {
        return User::ofPlatform()
            ->where('agent_id', -1)
            ->where('is_agent', 1)
            ->with('salesman')
            ->get(['id', 'salesman_id', 'name']);
    }

    /**
     * 获取币种汇率
     *
     * @return Collection
     */
    protected function getCurrencies()
    {
        return (new Currency())->getLatestAvailableCurrencies();
    }

    /**
     * 填入自定义字段
     *
     * @param Spreadsheet $xlsx
     * @return void
     */
    protected function setOtherColumns(Spreadsheet $xlsx)
    {
        $category = ProductOfflineCategory::with('fields')->findOrFail($this->categoryId);
        $sheet = $xlsx->getSheetByName('清单');
        $lastColumnCode = $sheet->getHighestColumn();
        $index = 1;
        if ($category->fields()->exists()) {
            $category['fields']->each(function ($field) use ($sheet, $lastColumnCode, &$index) {
                $lastColumnCode = chr(ord($lastColumnCode) + $index);
                $sheet->setCellValue("{$lastColumnCode}1", $field['name']);
                $sheet->getStyle("{$lastColumnCode}1")
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('ffff00');

                switch ($field['type']) {
                    case ProductOfflineFieldType::Date:
                        $this->setDateField($sheet, $field, $lastColumnCode);
                        break;
                    case ProductOfflineFieldType::Select:
                        $this->setDropdownField($sheet, $field, $lastColumnCode);
                        break;
                    default:
                        break;
                }

                $index++;
            });
        }
    }

    /**
     * 设置日期字段
     *
     * @param  \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet
     * @param  \App\Models\ProductOfflineField $field
     * @param  string $column
     * @return void
     */
    protected function setDateField(Worksheet $sheet, ProductOfflineField $field, string $column)
    {
        $sheet->setCellValue("{$column}2", '2024-01-01');

        // 设置整列的日期格式（除标题行外）
        $range = "{$column}2:{$column}1048576";
        $sheet->getStyle($range)
            ->getNumberFormat()
            ->setFormatCode('yyyy/mm/dd');
    }

    /**
     * 设置下拉选项
     *
     * @param  \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet
     * @param  \App\Models\ProductOfflineField $field
     * @param  string $column
     *
     * @return void
     */
    protected function setDropdownField(Worksheet $sheet, ProductOfflineField $field, string $column)
    {
        $options = preg_split('/\\r\\n|\\r|\\n/', $field->options);

        $validation = new DataValidation();
        $validation->setType(DataValidation::TYPE_LIST);
        $validation->setAllowBlank(true);
        $validation->setShowDropDown(true);
        $validation->setErrorTitle('错误');
        $validation->setError('请选择有效的选项');
        $validation->setFormula1('"' . implode(',', array_map('trim', $options)) . '"');

        // 设置整列的下拉验证（除标题行外）
        $range = "{$column}2:{$column}1048576";
        $sheet->setDataValidation($range, $validation);
    }

    /**
     * 设置出单公司内容
     *
     * @param Spreadsheet $xlsx
     * @param $branches
     * @return void
     */
    protected function setCompanyBranchContent(Spreadsheet $xlsx, $branches)
    {
        $rowIndex = 1;
        $branches->each(function ($branch) use ($xlsx, &$rowIndex) {
            $sheet = $xlsx->getSheetByName('出单公司');
            $sheet->setCellValue("A{$rowIndex}", $branch['company']['name'] . '-' . $branch['id'] . '-' . $branch['name']);
            $rowIndex++;
        });
    }

    /**
     * 设置用户/代理数据
     *
     * @param Spreadsheet $xlsx
     * @param $sheetName
     * @param $data
     * @return void
     */
    protected function setUserOrAgentContent(Spreadsheet $xlsx, $sheetName, $data)
    {
        $rowIndex = 1;
        $data->each(function ($item) use ($xlsx, $sheetName, &$rowIndex) {
            $sheet = $xlsx->getSheetByName($sheetName);
            $sheet->setCellValue("A{$rowIndex}", $item['salesman']['name'] . '-' . $item['id'] . '-' . $item['name']);
            $rowIndex++;
        });
    }

    /**
     * 设置币种数据
     *
     * @param Spreadsheet $xlsx
     * @param string $sheetName
     * @param $data
     * @param string $value
     * @return void
     */
    protected function setCurrencyContent(Spreadsheet $xlsx, string $sheetName, $data, string $value = 'name')
    {
        $sheet = $xlsx->getSheetByName($sheetName);

        foreach ($data as $idx => $item) {
            $sheetIndex = $idx + 1;
            $sheet->setCellValue("A{$sheetIndex}", $item[$value]);
        }
    }


    /**
     * 填充表格内容
     *
     * @param   Spreadsheet  $xlsx
     * @param   string       $sheetName
     * @param   Collection   $data
     * @param   string       $column
     * @param   string       $value
     *
     * @return  void
     */
    protected function setSheetContent(Spreadsheet $xlsx, string $sheetName, $data, string $column = 'id', string $value = 'name')
    {
        $sheet = $xlsx->getSheetByName($sheetName);

        foreach ($data as $idx => $item) {
            $sheetIndex = $idx + 1;
            $sheet->setCellValue("A{$sheetIndex}", $item[$column] . '-' . $item[$value]);
        }
    }

    /**
     * 模板路径
     *
     * @return  string
     */
    abstract protected function templatePath(): string;
}
