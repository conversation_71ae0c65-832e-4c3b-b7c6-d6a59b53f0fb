<?php

namespace App\Services\Policy\Offline\ImportTempateGenerator;

class TemplatePath extends CommonGenerator implements Generator
{

    /**
     * 类别ID
     *
     * @param $categoryId
     */
    public function __construct($categoryId)
    {
        $this->categoryId = $categoryId;
    }

    /**
     * 模板路径
     *
     * @return  string
     */
    protected function templatePath(): string
    {
        return resource_path('stubs/policy/offline/线下录入保单导入模板.xlsx');
    }
}
