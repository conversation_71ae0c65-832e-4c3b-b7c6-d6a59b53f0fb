<?php

namespace App\Services\Metric;

use App\Models\Company;
use App\Models\Policy;
use App\Models\User;
use Auth;
use Cache;
use DB;
use Illuminate\Database\Eloquent\Model;
use Spatie\QueryBuilder\QueryBuilder;

class FilterOption
{
    /**
     * 获取筛选选项
     *
     * @return array
     */
    public function options(): array
    {
        return Cache::remember(
            'metric:filter_option',
            60 * 60 * 24,
            fn() => [
                'business_from' => $this->getBusinessForm(),
                'years' => $this->years(),
                'product_types' => $this->productTypes(),
                'companies' => $this->uniqueCompanies(),
                // 'users' => $this->uniqueUsers(),
            ]
        );
    }

    /**
     * 获取产品类型选项
     *
     * @return array
     */
    protected function productTypes()
    {
        return DB::table('policy_types')
            ->select('product_type', 'name')
            ->groupBy('product_type')
            ->get()
            ->map(fn($item) => [
                'value' => $item->product_type,
                'label' => $item->name,
            ])
            ->toArray();
    }

    /**
     * 获取年份选项
     *
     * @return array
     */
    protected function years()
    {
        return Policy::ofPlatformUsers()
            ->where('status', Policy::STATUS_ISSUED)
            ->groupByRaw('year(submitted_at)')
            ->selectRaw('year(submitted_at) as year')
            ->orderByDesc('year')
            ->get()
            ->pluck('year')
            ->filter()
            ->take(2)
            ->toArray();
    }

    /**
     * 获取业务来源选项
     *
     * @return array
     */
    protected function getBusinessForm(): array
    {
        $platformId = Auth::user()['platform_id'];

        return User::whereHas('policies', fn($q) => $q->where('platform_id', $platformId))
            ->groupBy('platform_id')
            ->with('platform:id,name')
            ->get(['id', 'platform_id'])
            ->pluck('platform')
            ->filter(fn($item) => $item['id'] !== $platformId)
            ->prepend([
                'id' => $platformId,
                'name' => '自有业务',
            ])
            ->map(fn($item) => [
                'value' => $item['id'],
                'label' => $item['name'],
            ])
            ->toArray();
    }

    /**
     * 获取出单公司信息
     *
     * @return array
     */
    protected function uniqueCompanies()
    {
        $newCompanyModel = new class extends Company {
            protected $table = 'companies';

            public function branches()
            {
                $branchModel = new class extends Model {
                    protected $table = 'fake_company_branches';
                };
                return $this->hasMany($branchModel, 'company_id', 'id');
            }
        };

        return $newCompanyModel->with(['branches'])
            ->get(['id', 'name'])
            ->map(fn ($item) => [
                    'value' => $item->id,
                    'label' => $item->name,
                    'children' => $item->branches->unique('name')->map(fn ($branch) => [
                        'value' => $branch->fake_id,
                        'label' => $branch->name,
                    ])->toArray(),
            ])
            ->toArray();
    }

    /**
     * 获取用户选项
     *
     * @return array
     */
    public function searchUsers()
    {
        $model = new class extends Model {
            protected $table = 'fake_users';
        };

        return QueryBuilder::for($model)
            ->allowedFilters(['name'])
            ->limit(10)
            ->get()
            ->unique('name')
            ->map(fn($item) => [
                'value' => $item->fake_id,
                'label' => $item->name,
            ])
            ->toArray();
    }
}
