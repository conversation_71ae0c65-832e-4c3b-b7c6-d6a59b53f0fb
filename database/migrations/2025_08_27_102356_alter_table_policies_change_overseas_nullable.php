<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('ALTER TABLE policies MODIFY COLUMN policyholder_overseas TINYINT(1) NULL DEFAULT 0');
        DB::statement('ALTER TABLE policies MODIFY COLUMN insured_overseas TINYINT(1) NULL DEFAULT 0');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('ALTER TABLE policies MODIFY COLUMN policyholder_overseas TINYINT(1) NOT NULL DEFAULT 0');
        DB::statement('ALTER TABLE policies MODIFY COLUMN insured_overseas TINYINT(1) NOT NULL DEFAULT 0');
    }
};
