<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('policy_cargos', function (Blueprint $table) {
            $table->bigInteger('subject_category_id')->default(-1)->comment('标的小类 ID')->after('subject_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('policy_cargos', function (Blueprint $table) {
            $table->dropColumn('subject_category_id');
        });
    }
};
